import warnings
from pathlib import Path
from typing import Any

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
from scipy import stats

plt.style.use("seaborn")
plt.rcParams["figure.figsize"] = (12, 8)
plt.rcParams["font.size"] = 10

def load_data(file_path: str, file_type: str | None = None, **kwargs: dict[Any, Any]) -> pd.DataFrame:
    """
    Загружает данные из файла с автоматическим определением типа.

    Args:
        file_path: Путь к файлу данных
        file_type: Тип файла ('excel', 'csv', 'json'). Если None, определяется автоматически по расширению
        **kwargs: Дополнительные параметры для pandas функций чтения

    Returns:
        pd.DataFrame: Загруженные данные

    Raises:
        FileNotFoundError: Если файл не существует
        ValueError: Если расширение файла не поддерживается

    """
    if not Path(file_path).exists():
        msg = f"Файл не найден: {file_path}"
        raise FileNotFoundError(msg)

    if file_type is None:
        path = Path(file_path)
        suffix = path.suffix.lower()

        if suffix in [".xlsx", ".xls"]:
            file_type = "excel"
        elif suffix == ".csv":
            file_type = "csv"
        elif suffix == ".json":
            file_type = "json"
        else:
            msg = f"Неподдерживаемое расширение файла: {suffix}"
            raise ValueError(msg)

    if file_type == "excel":
        df = pd.read_excel(file_path, **kwargs)
    elif file_type == "csv":
        df = pd.read_csv(file_path, **kwargs)
    elif file_type == "json":
        df = pd.read_json(file_path, **kwargs)
    else:
        msg = f"Неподдерживаемый тип файла: {file_type}"
        raise ValueError(msg)

    return df


def validate_required_columns(df: pd.DataFrame, required_columns: list[str]) -> None:
    """
    Проверяет наличие обязательных колонок в DataFrame.
    """
    missing_columns = set(required_columns) - set(df.columns)

    if missing_columns:
        msg = f"Отсутствуют обязательные колонки: {missing_columns}"
        raise ValueError(msg)


def check_duplicates(df: pd.DataFrame, id_column: str = "id_order") -> None:
    """
    Проверяет наличие дубликатов в колонке ID.
    """
    if id_column not in df.columns:
        return

    duplicates = df[id_column].duplicated().sum()

    if duplicates > 0:
        warnings.warn(f"Обнаружено {duplicates} дублированных ID", stacklevel=2)


def analyze_missing_values(df: pd.DataFrame, threshold: float = 50.0) -> dict[str, float]:
    """
    Анализирует пропущенные значения в DataFrame.
    """
    missing_stats = df.isna().sum()
    missing_percent = (missing_stats / len(df)) * 100

    result = {}

    if missing_stats.sum() == 0:
        return result

    for col in missing_stats[missing_stats > 0]:
        percent = missing_percent[col]
        result[col] = percent

        if percent > threshold:
            warnings.warn(f"Колонка {col} содержит более {threshold}% пропущенных значений", stacklevel=2)

    return result

def convert_time_column(
    series: pd.Series, time_format: str = "%d.%m.%y %H:%M", column_name: str = "column"
) -> pd.Series:
    """
    Преобразует одну колонку в формат datetime.
    """
    if series.dtype == "datetime64[ns]":
        return series

    return pd.to_datetime(series, format=time_format)


def convert_time_columns(
    df: pd.DataFrame, time_columns: list[str], time_format: str = "%d.%m.%y %H:%M"
) -> pd.DataFrame:
    """
    Преобразует несколько временных колонок.
    """
    df_result = df.copy()

    for col in time_columns:
        if col in df_result.columns:
            df_result[col] = convert_time_column(df_result[col], time_format, col)

    return df_result


def add_time_derived_columns(df: pd.DataFrame, base_column: str = "order_time") -> pd.DataFrame:
    """
    Добавляет производные временные колонки.
    """
    df_result = df.copy()

    if base_column not in df_result.columns:
        return df_result

    if df_result[base_column].dtype != "datetime64[ns]":
        return df_result

    # Добавляем производные колонки
    df_result["day_order"] = df_result[base_column].dt.day
    df_result["hour_order"] = df_result[base_column].dt.floor("h")
    df_result["weekday"] = df_result[base_column].dt.dayofweek
    df_result["month"] = df_result[base_column].dt.month

    return df_result

def create_aggregation_config(
    df: pd.DataFrame, metrics_config: dict[str, str] | None = None
) -> dict[str, tuple[str, str]]:
    """
    Создает конфигурацию для агрегации данных.
    """
    if metrics_config is None:
        metrics_config = {
            "cnt_order": "id_order",
            "cnt_offer": "offer_time",
            "cnt_assign": "assign_time",
            "cnt_arrive": "arrive_time",
            "cnt_trip": "trip_time",
        }

    agg_dict = {}

    for metric_name, column in metrics_config.items():
        if column in df.columns:
            agg_dict[metric_name] = (column, "count")

    return agg_dict


def group_and_aggregate(
    df: pd.DataFrame,
    group_by: str | list[str],
    agg_config: dict[str, tuple[str, str]],
) -> pd.DataFrame:
    """
    Группирует и агрегирует данные.
    """
    try:
        return df.groupby(group_by, as_index=False).agg(agg_config)
    except Exception as e:
        msg = f"Ошибка при группировке: {e}"
        raise ValueError(msg)


def calculate_conversion(df: pd.DataFrame, numerator_col: str, denominator_col: str, conversion_name: str) -> pd.Series:
    """
    Рассчитывает одну метрику конверсии.
    """
    if numerator_col not in df.columns:
        msg = f"Колонка {numerator_col} не найдена"
        raise ValueError(msg)
    if denominator_col not in df.columns:
        msg = f"Колонка {denominator_col} не найдена"
        raise ValueError(msg)

    # Избегаем деления на ноль
    return df[numerator_col] / df[denominator_col].replace(0, np.nan)


def calculate_all_conversions(df: pd.DataFrame) -> pd.DataFrame:
    """
    Рассчитывает все стандартные конверсии такси.
    """
    df_result = df.copy()

    # Определяем конверсии для расчета
    conversions = [
        ("cnt_trip", "cnt_order", "order2trip"),
        ("cnt_offer", "cnt_order", "order2offer"),
        ("cnt_assign", "cnt_offer", "offer2assign"),
        ("cnt_arrive", "cnt_assign", "assign2arrive"),
        ("cnt_trip", "cnt_arrive", "arrive2trip"),
    ]

    for num_col, den_col, conv_name in conversions:
        df_result[conv_name] = calculate_conversion(df_result, num_col, den_col, conv_name)

    # Обработка бесконечных значений
    return df_result.replace([np.inf, -np.inf], np.nan)


def setup_plot_style(figsize: tuple[int, int] = (12, 8), style: str = "default") -> None:
    """
    Настраивает стиль графика.
    """
    plt.style.use(style)
    plt.figure(figsize=figsize)


def get_city_data(df: pd.DataFrame, cities: list[str] | None = None) -> tuple[list[str], list[pd.DataFrame]]:
    """
    Получает данные для указанных городов.
    """
    if "city" not in df.columns:
        msg = "Колонка 'city' не найдена в данных"
        raise ValueError(msg)

    available_cities = df["city"].unique().tolist()

    if cities is None:
        cities = available_cities
    else:
        # Проверяем доступность городов
        missing_cities = [city for city in cities if city not in available_cities]
        if missing_cities:
            cities = [city for city in cities if city in available_cities]

    if not cities:
        msg = "Нет доступных городов для отображения"
        raise ValueError(msg)

    # Получаем данные для каждого города
    city_data_list = []
    for city in cities:
        city_data = df[df["city"] == city]
        city_data_list.append(city_data)

    return cities, city_data_list


def plot_city_lines(cities: list[str], city_data_list: list[pd.DataFrame], x_column: str, y_column: str) -> None:
    """
    Строит линии для каждого города на графике.
    """
    colors = plt.cm.Set1(np.linspace(0, 1, len(cities)))

    for i, (city, city_data) in enumerate(zip(cities, city_data_list, strict=False)):
        if len(city_data) == 0 or x_column not in city_data.columns or y_column not in city_data.columns:
            continue

        plt.plot(
            city_data[x_column],
            city_data[y_column],
            label=city,
            color=colors[i],
            marker="o",
            linewidth=2,
            markersize=6,
        )


def setup_plot_labels(
    title: str | None,
    x_column: str,
    y_column: str,
    y_limit: tuple[float, float] | None = None,
) -> None:
    """
    Настраивает подписи и заголовки графика.
    """
    plt.legend(bbox_to_anchor=(1.05, 1), loc="upper left")
    plt.grid(visible=True, alpha=0.3)

    if title:
        plt.title(title, fontsize=14, fontweight="bold")
    else:
        plt.title(f"{y_column} по городам", fontsize=14, fontweight="bold")

    plt.xlabel(x_column.replace("_", " ").title(), fontsize=12)
    plt.ylabel(y_column.replace("_", " ").title(), fontsize=12)

    if y_limit:
        plt.ylim(y_limit)

    plt.tight_layout()

def plot_metric_by_cities(
    df: pd.DataFrame,
    metric_column: str,
    x_column: str = "day_order",
    cities: list[str] | None = None,
    title: str | None = None,
    y_limit: tuple[float, float] | None = None,
    figsize: tuple[int, int] = (12, 8),
    style: str = "default",
    save_path: str | None = None,
) -> None:
    """
    Строит график метрики по городам.
    """
    # Проверка входных данных
    required_cols = [metric_column, x_column]
    missing_cols = [col for col in required_cols if col not in df.columns]
    if missing_cols:
        msg = f"Отсутствуют колонки: {missing_cols}"
        raise ValueError(msg)

    # Настройка графика
    setup_plot_style(figsize, style)

    # Получение данных по городам
    cities, city_data_list = get_city_data(df, cities)

    # Построение линий
    plot_city_lines(cities, city_data_list, x_column, metric_column)

    # Настройка подписей
    setup_plot_labels(title, x_column, metric_column, y_limit)

    # Сохранение
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches="tight")

    plt.show()

def detect_outliers_iqr(data: pd.Series, threshold: float = 1.5) -> pd.Series:
    """
    Детекция выбросов методом межквартильного размаха (IQR).
    """
    q1 = data.quantile(0.25)
    q3 = data.quantile(0.75)
    iqr = q3 - q1

    lower_bound = q1 - threshold * iqr
    upper_bound = q3 + threshold * iqr

    return (data < lower_bound) | (data > upper_bound)


def detect_outliers_zscore(data: pd.Series, threshold: float = 3.0) -> pd.Series:
    """
    Детекция выбросов методом Z-score.
    """
    z_scores = np.abs(stats.zscore(data.dropna()))
    outlier_mask = pd.Series(data=False, index=data.index)
    outlier_mask.loc[data.dropna().index] = z_scores > threshold

    return outlier_mask


def analyze_column_outliers(
    df: pd.DataFrame, column: str, method: str = "iqr", threshold: float = 1.5
) -> dict[str, Any]:
    """
    Анализирует выбросы в одной колонке.
    """
    if column not in df.columns:
        msg = f"Колонка {column} не найдена"
        raise ValueError(msg)

    data = df[column].dropna()
    if len(data) == 0:
        return {"column": column, "outliers_count": 0, "outliers_percentage": 0.0}

    # Выбор метода детекции
    if method == "iqr":
        outlier_mask = detect_outliers_iqr(data, threshold)
    elif method == "zscore":
        outlier_mask = detect_outliers_zscore(data, threshold)
    else:
        msg = f"Неподдерживаемый метод: {method}"
        raise ValueError(msg)

    outliers_count = outlier_mask.sum()
    outliers_percentage = (outliers_count / len(data)) * 100

    return {
        "column": column,
        "outliers_count": outliers_count,
        "outliers_percentage": outliers_percentage,
        "method": method,
        "threshold": threshold,
        "outlier_indices": data[outlier_mask].index.tolist(),
    }


def find_conversion_anomalies(
    df: pd.DataFrame,
    conversion_column: str,
    group_column: str = "city",
    low_threshold: float = 0.1,
    high_threshold: float = 0.9,
) -> dict[str, list[dict]]:
    """
    Находит аномалии в конверсиях по группам.
    """
    if conversion_column not in df.columns:
        msg = f"Колонка {conversion_column} не найдена"
        raise ValueError(msg)
    if group_column not in df.columns:
        msg = f"Колонка {group_column} не найдена"
        raise ValueError(msg)

    anomalies = {"low_conversion": [], "high_conversion": [], "zero_conversion": []}

    for group_value in df[group_column].unique():
        group_data = df[df[group_column] == group_value][conversion_column].dropna()

        if len(group_data) == 0:
            continue

        low_count = (group_data < low_threshold).sum()
        high_count = (group_data > high_threshold).sum()
        zero_count = (group_data == 0).sum()

        if low_count > 0:
            anomalies["low_conversion"].append(
                {
                    "group": group_value,
                    "count": low_count,
                    "percentage": (low_count / len(group_data)) * 100,
                }
            )

        if high_count > 0:
            anomalies["high_conversion"].append(
                {
                    "group": group_value,
                    "count": high_count,
                    "percentage": (high_count / len(group_data)) * 100,
                }
            )

        if zero_count > 0:
            anomalies["zero_conversion"].append(
                {
                    "group": group_value,
                    "count": zero_count,
                    "percentage": (zero_count / len(group_data)) * 100,
                }
            )

    return anomalies

def perform_complete_analysis(
    file_path: str,
    required_columns: list[str] | None = None,
    time_columns: list[str] | None = None,
    group_by: str | list[str] | None = None,
) -> tuple[pd.DataFrame, pd.DataFrame]:
    """
    Выполняет полный анализ данных такси, используя модульные функции.
    """
    # 1. Загрузка данных
    if group_by is None:
        group_by = ["day_order", "city"]
    df = load_data(file_path)

    # 2. Валидация
    if required_columns is None:
        required_columns = [
            "id_order",
            "order_time",
            "offer_time",
            "assign_time",
            "arrive_time",
            "trip_time",
            "city",
        ]

    validate_required_columns(df, required_columns)
    check_duplicates(df)
    analyze_missing_values(df)

    # 3. Обработка времени
    if time_columns is None:
        time_columns = [
            "order_time",
            "offer_time",
            "assign_time",
            "arrive_time",
            "trip_time",
        ]

    df = convert_time_columns(df, time_columns)
    df = add_time_derived_columns(df)

    # 4. Расчет метрик
    agg_config = create_aggregation_config(df)
    df_metrics = group_and_aggregate(df, group_by, agg_config)
    df_metrics = calculate_all_conversions(df_metrics)

    return df, df_metrics

# Выполняем полный анализ одной функцией
try:
    df_original, df_metrics = perform_complete_analysis("taxi_data.xlsx")
except FileNotFoundError:
    # Создание демонстрационных данных
    np.random.seed(42)
    dates = pd.date_range("2024-01-01", periods=100, freq="D")

    df_demo = pd.DataFrame(
        {
            "id_order": range(1, 101),
            "order_time": dates,
            "offer_time": dates + pd.Timedelta(minutes=1),
            "assign_time": dates + pd.Timedelta(minutes=2),
            "arrive_time": dates + pd.Timedelta(minutes=5),
            "trip_time": dates + pd.Timedelta(minutes=10),
            "city": np.random.choice(["Москва", "СПб", "Казань"], 100),
        }
    )

    # Сохраняем демо-данные и анализируем
    df_demo.to_excel("taxi_data.xlsx", index=False)
    df_original, df_metrics = perform_complete_analysis("taxi_data.xlsx")

print(f"✓ Исходные данные: {df_original.shape}")
print(f"✓ Агрегированные метрики: {df_metrics.shape}")

# Визуализация ключевых метрик
if "cnt_order" in df_metrics.columns:
    plot_metric_by_cities(df_metrics, "cnt_order", title="Количество заказов")

if "order2trip" in df_metrics.columns:
    plot_metric_by_cities(df_metrics, "order2trip", title="Order2Trip конверсия", y_limit=(0, 1))

# Анализ выбросов в исходных данных
print("=== АНАЛИЗ ВЫБРОСОВ ===")

# Анализируем числовые колонки
numeric_columns = ["day_order"]

for column in numeric_columns:
    if column in df_original.columns:
        outlier_results = analyze_column_outliers(df_original, column, method="iqr")
        print(f"Результаты для {column}: {outlier_results['outliers_count']} выбросов")

# Анализ аномалий в конверсиях
print("\n=== АНАЛИЗ АНОМАЛИЙ В КОНВЕРСИЯХ ===")

conversion_columns = ["order2trip", "order2offer", "offer2assign"]

for conv_col in conversion_columns:
    if conv_col in df_metrics.columns:
        anomalies = find_conversion_anomalies(df_metrics, conv_col)

        print(f"\nАномалии в {conv_col}:")
        if anomalies["low_conversion"]:
            print(f"  Низкие конверсии: {len(anomalies['low_conversion'])} групп")
        if anomalies["high_conversion"]:
            print(f"  Высокие конверсии: {len(anomalies['high_conversion'])} групп")
        if anomalies["zero_conversion"]:
            print(f"  Нулевые конверсии: {len(anomalies['zero_conversion'])} групп")

print("\n✓ Анализ завершен")